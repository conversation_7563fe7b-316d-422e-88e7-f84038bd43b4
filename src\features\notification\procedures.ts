import { createPermissionProcedure } from '@/lib/procedures';

export const viewNotificationsPermissionProcedure =
  createPermissionProcedure('Ver notificaciones');
export const createNotificationsPermissionProcedure = createPermissionProcedure(
  'Crear notificaciones',
);
export const editNotificationsPermissionProcedure = createPermissionProcedure(
  'Editar notificaciones',
);
export const deleteNotificationsPermissionProcedure = createPermissionProcedure(
  'Eliminar notificaciones',
);
