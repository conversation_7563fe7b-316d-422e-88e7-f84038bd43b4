import { createPermissionProcedure } from '@/lib/procedures';

export const viewCreditorsPermissionProcedure =
  createPermissionProcedure('Ver acreedores');
export const createCreditorsPermissionProcedure =
  createPermissionProcedure('Crear acreedores');
export const editCreditorsPermissionProcedure =
  createPermissionProcedure('Editar acreedores');
export const deleteCreditorsPermissionProcedure = createPermissionProcedure(
  'Eliminar acreedores',
);
