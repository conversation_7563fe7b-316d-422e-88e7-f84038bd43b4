'use client';

import { useState, useEffect } from 'react';
import { useServerAction } from 'zsa-react';

import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import type {
  DocumentFromDrive,
  DocumentTemplate,
} from '@/features/document/schemas';
import {
  getTemplatesFromDatabase,
  getDocumentsFromDrive,
} from '@/features/document/actions';

import { DocumentFileTree } from './document-file-tree';
import { DocumentTemplateLibrary } from './document-template-library';

interface DocumentTabsProps {
  refreshTrigger: number;
}

export function DocumentTabs({ refreshTrigger }: Readonly<DocumentTabsProps>) {
  const [documentsFromDrive, setDocumentsFromDrive] = useState<
    DocumentFromDrive[]
  >([]);
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);

  const { execute: loadTemplates } = useServerAction(getTemplatesFromDatabase, {
    onSuccess: ({ data }) => {
      setTemplates(data);
    },
  });

  const { execute: loadDocumentsFromDrive } = useServerAction(
    getDocumentsFromDrive,
    {
      onSuccess: ({ data }) => {
        setDocumentsFromDrive(data);
      },
    },
  );

  useEffect(() => {
    loadTemplates({});
    loadDocumentsFromDrive({});
  }, [loadTemplates, loadDocumentsFromDrive, refreshTrigger]);

  return (
    <Tabs defaultValue="documents" className="space-y-4">
      <TabsList>
        <TabsTrigger value="documents">Documentos</TabsTrigger>
        <TabsTrigger value="templates">Plantillas</TabsTrigger>
      </TabsList>

      <TabsContent value="documents" className="space-y-4">
        <DocumentFileTree documentsFromDrive={documentsFromDrive} />
      </TabsContent>

      <TabsContent value="templates">
        <DocumentTemplateLibrary templates={templates} />
      </TabsContent>
    </Tabs>
  );
}
