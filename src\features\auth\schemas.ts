import { z } from 'zod';

export const loginSchema = z.object({
  email: z.string().email('Email inválido'),
  password: z.string().min(1, 'La contraseña es requerida'),
  database: z.string().min(1, 'La base de datos es requerida'),
});

export const sessionSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  roleId: z.string(),
  roleName: z.string(),
  permissions: z.array(z.string()),
  database: z.string(),
  lastLogin: z.date(),
  iat: z.number(),
  exp: z.number(),
});

export const refreshTokenSchema = z.object({
  userId: z.string(),
  database: z.string(),
  iat: z.number(),
  exp: z.number(),
});

export const authResultSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  user: sessionSchema.omit({ iat: true, exp: true }).optional(),
  accessToken: z.string().optional(),
  refreshToken: z.string().optional(),
});

export const logoutResultSchema = z.object({
  success: z.boolean(),
  message: z.string(),
});

export const validateSessionResultSchema = z.object({
  valid: z.boolean(),
  user: sessionSchema.omit({ iat: true, exp: true }).optional(),
  error: z.string().optional(),
});

export const permissionCheckSchema = z.object({
  hasPermission: z.boolean(),
  userPermissions: z.array(z.string()),
  requiredPermission: z.string(),
});

export const permissionGuardPropsSchema = z.object({
  permission: z.string(),
});

export type LoginInput = z.infer<typeof loginSchema>;
export type SessionData = z.infer<typeof sessionSchema>;
export type RefreshTokenData = z.infer<typeof refreshTokenSchema>;
export type AuthResult = z.infer<typeof authResultSchema>;
export type LogoutResult = z.infer<typeof logoutResultSchema>;
export type ValidateSessionResult = z.infer<typeof validateSessionResultSchema>;
export type PermissionCheck = z.infer<typeof permissionCheckSchema>;
export type PermissionGuardProps = z.infer<typeof permissionGuardPropsSchema>;
