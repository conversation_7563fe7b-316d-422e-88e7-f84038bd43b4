'use client';

import { useCallback, useEffect, useState } from 'react';
import { useServerAction } from 'zsa-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { NotificationItem } from '@/components/notifications/notification-item';
import {
  getNotifications,
  getNotificationStats,
} from '@/features/notification/actions';
import { NotificationWithCase } from '@/features/notification/schemas';

import { DashboardHeader } from '../../dashboard/components/dashboard-header';

export function NotificationManagement() {
  const [notifications, setNotifications] = useState<NotificationWithCase[]>(
    [],
  );
  const [stats, setStats] = useState({
    total: 0,
  });

  const { execute: fetchNotifications } = useServerAction(getNotifications);
  const { execute: fetchStats } = useServerAction(getNotificationStats);

  const loadNotifications = useCallback(
    async (filters?: {
      type?:
        | 'case_created'
        | 'case_updated'
        | 'case_deleted'
        | 'creditor_created'
        | 'creditor_updated'
        | 'creditor_deleted'
        | 'debtor_created'
        | 'debtor_updated'
        | 'debtor_deleted'
        | 'user_created'
        | 'user_updated'
        | 'user_deleted'
        | 'role_created'
        | 'role_updated'
        | 'role_deleted';
    }) => {
      const [notificationsResult, statsResult] = await Promise.all([
        fetchNotifications(filters || {}),
        fetchStats({}),
      ]);

      if (notificationsResult?.[0]) {
        setNotifications(notificationsResult[0].notifications);
      }

      if (statsResult?.[0]) {
        setStats({
          total: statsResult[0].total,
        });
      }
    },
    [fetchNotifications, fetchStats],
  );

  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Centro de Notificaciones
              </h1>
              <p className="text-gray-600">
                Gestione alertas y notificaciones del sistema
              </p>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Todas las Notificaciones</CardTitle>
              <CardDescription>
                Lista completa de notificaciones del sistema ({stats.total})
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {notifications.map((notification) => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    variant="full"
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
