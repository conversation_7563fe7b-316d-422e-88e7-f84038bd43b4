'use server';

import { revalidatePath } from 'next/cache';

import prisma from '@/lib/prisma';

import {
  viewNotificationsPermissionProcedure,
  deleteNotificationsPermissionProcedure,
} from './procedures';
import {
  getNotificationsInputSchema,
  getNotificationsOutputSchema,
  deleteNotificationInputSchema,
  deleteNotificationOutputSchema,
  getNotificationStatsInputSchema,
  getNotificationStatsOutputSchema,
} from './schemas';

export const getNotifications = viewNotificationsPermissionProcedure
  .createServerAction()
  .input(getNotificationsInputSchema)
  .output(getNotificationsOutputSchema)
  .handler(async ({ input: { userId, type, limit, offset }, ctx }) => {
    const where = {
      userId: userId || ctx.user.id,
      ...(type && { type }),
    };

    const [notificationsRaw, total] = await Promise.all([
      prisma.notification.findMany({
        where,
        include: {
          case: {
            select: {
              id: true,
              caseNumber: true,
              debtorName: true,
              type: true,
              status: true,
            },
          },
        },
        orderBy: [{ createdDate: 'desc' }],
        take: limit,
        skip: offset,
      }),
      prisma.notification.count({ where }),
    ]);

    const notifications = notificationsRaw.map((notification) => ({
      ...notification,
      type: notification.type as
        | 'case_created'
        | 'case_updated'
        | 'case_deleted'
        | 'creditor_created'
        | 'creditor_updated'
        | 'creditor_deleted'
        | 'debtor_created'
        | 'debtor_updated'
        | 'debtor_deleted'
        | 'user_created'
        | 'user_updated'
        | 'user_deleted'
        | 'role_created'
        | 'role_updated'
        | 'role_deleted',
    }));

    return {
      notifications,
      total,
    };
  });

export const deleteNotification = deleteNotificationsPermissionProcedure
  .createServerAction()
  .input(deleteNotificationInputSchema)
  .output(deleteNotificationOutputSchema)
  .handler(async ({ input: { notificationId }, ctx }) => {
    const notification = await prisma.notification.findUnique({
      where: { id: notificationId },
    });

    if (!notification) {
      throw new Error('Notificación no encontrada');
    }

    if (notification.userId !== ctx.user.id) {
      throw new Error('No tienes permisos para eliminar esta notificación');
    }

    await prisma.notification.delete({
      where: { id: notificationId },
    });

    revalidatePath('/notifications');
    revalidatePath('/dashboard');

    return { success: true };
  });

export const getNotificationStats = viewNotificationsPermissionProcedure
  .createServerAction()
  .input(getNotificationStatsInputSchema)
  .output(getNotificationStatsOutputSchema)
  .handler(async ({ input: { userId }, ctx }) => {
    const finalUserId = userId || ctx.user.id;

    const [total, byType] = await Promise.all([
      prisma.notification.count({ where: { userId: finalUserId } }),
      Promise.all([
        prisma.notification.count({
          where: { userId: finalUserId, type: 'case_created' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'case_updated' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'case_deleted' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'creditor_created' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'creditor_updated' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'creditor_deleted' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'debtor_created' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'debtor_updated' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'debtor_deleted' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'user_created' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'user_updated' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'user_deleted' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'role_created' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'role_updated' },
        }),
        prisma.notification.count({
          where: { userId: finalUserId, type: 'role_deleted' },
        }),
      ]),
    ]);

    return {
      total,
      byType: {
        case_created: byType[0],
        case_updated: byType[1],
        case_deleted: byType[2],
        creditor_created: byType[3],
        creditor_updated: byType[4],
        creditor_deleted: byType[5],
        debtor_created: byType[6],
        debtor_updated: byType[7],
        debtor_deleted: byType[8],
        user_created: byType[9],
        user_updated: byType[10],
        user_deleted: byType[11],
        role_created: byType[12],
        role_updated: byType[13],
        role_deleted: byType[14],
      },
    };
  });
