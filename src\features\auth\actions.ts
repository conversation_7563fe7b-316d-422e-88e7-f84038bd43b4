'use server';

import { revalidateTag } from 'next/cache';
import { createServerAction } from 'zsa';
import bcrypt from 'bcrypt';
import { cookies } from 'next/headers';
import { z } from 'zod';

import prisma from '@/lib/prisma';
import {
  createAccessToken,
  createRefreshToken,
  verifyToken,
  verifyRefreshToken,
  setAuthCookies,
  clearAuthCookies,
} from '@/lib/auth';

import {
  loginSchema,
  authResultSchema,
  logoutResultSchema,
  validateSessionResultSchema,
  permissionCheckSchema,
} from './schemas';

export const loginUser = createServerAction()
  .input(loginSchema)
  .output(authResultSchema)
  .handler(async ({ input: { email, password, database } }) => {
    try {
      const user = await prisma.user.findUnique({
        where: { email },
        include: {
          role: {
            include: {
              permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });

      if (!user || !user.password) {
        return {
          success: false,
          message: 'Credenciales inválidas. Verifique su correo y contraseña.',
        };
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return {
          success: false,
          message: 'Credenciales inválidas. Verifique su correo y contraseña.',
        };
      }

      await prisma.user.update({
        where: { id: user.id },
        data: { lastLogin: new Date() },
      });

      const sessionData = {
        id: user.id,
        name: user.name,
        email: user.email,
        roleId: user.roleId,
        roleName: user.role.name,
        permissions: user.role.permissions.map((rp) => rp.permission.name),
        database,
        lastLogin: new Date(),
      };

      const accessToken = await createAccessToken(sessionData);
      const refreshToken = await createRefreshToken({
        userId: user.id,
        database,
      });

      await setAuthCookies(accessToken, refreshToken);

      revalidateTag('auth');

      return {
        success: true,
        message: 'Inicio de sesión exitoso',
        user: sessionData,
        accessToken,
        refreshToken,
      };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: 'Error interno del servidor. Intente nuevamente.',
      };
    }
  });

export const logoutUser = createServerAction()
  .output(logoutResultSchema)
  .handler(async () => {
    try {
      await clearAuthCookies();
      revalidateTag('auth');

      return {
        success: true,
        message: 'Sesión cerrada exitosamente',
      };
    } catch (error) {
      console.error('Logout error:', error);
      return {
        success: false,
        message: 'Error al cerrar sesión',
      };
    }
  });

export const validateSession = createServerAction()
  .output(validateSessionResultSchema)
  .handler(async () => {
    try {
      const cookieStore = await cookies();
      const accessToken = cookieStore.get('access-token')?.value;

      if (!accessToken) {
        return {
          valid: false,
          error: 'No hay token de acceso',
        };
      }

      const payload = await verifyToken(accessToken);
      if (!payload) {
        return {
          valid: false,
          error: 'Token inválido',
        };
      }

      const user = await prisma.user.findUnique({
        where: { id: payload.id },
        include: {
          role: {
            include: {
              permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });

      if (!user) {
        return {
          valid: false,
          error: 'Usuario no encontrado',
        };
      }

      return {
        valid: true,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          roleId: user.roleId,
          roleName: user.role.name,
          permissions: user.role.permissions.map((rp) => rp.permission.name),
          database: payload.database,
          lastLogin: user.lastLogin || new Date(),
        },
      };
    } catch (error) {
      console.error('Session validation error:', error);
      return {
        valid: false,
        error: 'Error al validar sesión',
      };
    }
  });

export const checkUserPermission = createServerAction()
  .input(
    z.object({
      permission: z.string(),
    }),
  )
  .output(permissionCheckSchema)
  .handler(async ({ input: { permission } }) => {
    try {
      const cookieStore = await cookies();
      const accessToken = cookieStore.get('access-token')?.value;

      if (!accessToken) {
        return {
          hasPermission: false,
          userPermissions: [],
          requiredPermission: permission,
        };
      }

      const payload = await verifyToken(accessToken);
      if (!payload) {
        return {
          hasPermission: false,
          userPermissions: [],
          requiredPermission: permission,
        };
      }

      const hasPermission = payload.permissions.includes(permission);

      return {
        hasPermission,
        userPermissions: payload.permissions,
        requiredPermission: permission,
      };
    } catch (error) {
      console.error('Permission check error:', error);
      return {
        hasPermission: false,
        userPermissions: [],
        requiredPermission: permission,
      };
    }
  });

export const refreshAccessToken = createServerAction()
  .output(authResultSchema)
  .handler(async () => {
    try {
      const cookieStore = await cookies();
      const refreshToken = cookieStore.get('refresh-token')?.value;

      if (!refreshToken) {
        return {
          success: false,
          message: 'No hay token de actualización',
        };
      }

      const refreshPayload = await verifyRefreshToken(refreshToken);
      if (!refreshPayload) {
        return {
          success: false,
          message: 'Token de actualización inválido',
        };
      }

      const user = await prisma.user.findUnique({
        where: { id: refreshPayload.userId },
        include: {
          role: {
            include: {
              permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });

      if (!user) {
        return {
          success: false,
          message: 'Usuario no encontrado',
        };
      }

      const sessionData = {
        id: user.id,
        name: user.name,
        email: user.email,
        roleId: user.roleId,
        roleName: user.role.name,
        permissions: user.role.permissions.map((rp) => rp.permission.name),
        database: refreshPayload.database,
        lastLogin: user.lastLogin || new Date(),
      };

      const newAccessToken = await createAccessToken(sessionData);
      const newRefreshToken = await createRefreshToken({
        userId: user.id,
        database: refreshPayload.database,
      });

      await setAuthCookies(newAccessToken, newRefreshToken);

      return {
        success: true,
        message: 'Token actualizado exitosamente',
        user: sessionData,
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
      };
    } catch (error) {
      console.error('Token refresh error:', error);
      return {
        success: false,
        message: 'Error al actualizar token',
      };
    }
  });

export const switchDatabase = createServerAction()
  .output(logoutResultSchema)
  .handler(async () => {
    try {
      await clearAuthCookies();
      revalidateTag('auth');

      return {
        success: true,
        message: 'Base de datos cambiada exitosamente',
      };
    } catch (error) {
      console.error('Database switch error:', error);
      return {
        success: false,
        message: 'Error al cambiar base de datos',
      };
    }
  });
