'use server';

import prisma from '@/lib/prisma';
import { viewRolesPermissionProcedure } from '@/features/role/procedures';

import { getAllPermissionsSchema } from './schemas';

export const getAllPermissions = viewRolesPermissionProcedure
  .createServerAction()
  .output(getAllPermissionsSchema)
  .handler(async () => {
    return prisma.permission.findMany({
      orderBy: [{ category: 'asc' }, { name: 'asc' }],
    });
  });
