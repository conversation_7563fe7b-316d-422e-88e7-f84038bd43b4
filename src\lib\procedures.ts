import { cookies } from 'next/headers';
import { createServerActionProcedure } from 'zsa';

import prisma from '@/lib/prisma';
import { verifyToken } from '@/lib/auth';

export const authedProcedure = createServerActionProcedure().handler(
  async () => {
    try {
      const cookieStore = await cookies();
      const accessToken = cookieStore.get('access-token')?.value;

      if (!accessToken) {
        throw new Error('No hay token de acceso');
      }

      const payload = await verifyToken(accessToken);
      if (!payload) {
        throw new Error('Token inválido');
      }

      const user = await prisma.user.findUnique({
        where: { id: payload.id },
        include: {
          role: {
            include: {
              permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });

      if (!user) {
        throw new Error('Usuario no encontrado');
      }

      return {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          roleId: user.roleId,
          roleName: user.role.name,
          permissions: user.role.permissions.map((rp) => rp.permission.name),
          database: payload.database,
          lastLogin: user.lastLogin || new Date(),
        },
      };
    } catch {
      throw new Error('No autorizado');
    }
  },
);

export const createPermissionProcedure = (requiredPermission: string) =>
  createServerActionProcedure(authedProcedure).handler(
    async ({ ctx: { user } }) => {
      const hasPermission = user.permissions.includes(requiredPermission);

      if (!hasPermission) {
        throw new Error(`No tiene permisos para: ${requiredPermission}`);
      }

      return {
        user,
      };
    },
  );
