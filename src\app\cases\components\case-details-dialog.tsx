'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';

import type { CaseWithRelations } from '@/features/case/schemas';

interface CaseDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  case: CaseWithRelations;
  loading?: boolean;
}

export function CaseDetailsDialog({
  open,
  onOpenChange,
  case: caseData,
  loading = false,
}: Readonly<CaseDetailsDialogProps>) {
  if (!caseData) return null;

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Cargando detalles del caso...</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const creditors =
    caseData.debts?.map((debt) => ({
      name: debt.creditor?.name || 'Acreedor no especificado',
      amount: Number(debt.amount),
      type: debt.type || 'No especificado',
      interestRate: Number(debt.interestRate || 0),
    })) || [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>Detalles del Caso {caseData?.id || 'N/A'}</DialogTitle>
          <DialogDescription>
            Información completa del caso de {caseData?.debtorName || 'N/A'}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="debtor" className="space-y-4">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="debtor">Deudor</TabsTrigger>
            <TabsTrigger value="case-details">Detalles</TabsTrigger>
            <TabsTrigger value="legal-process">Proceso Legal</TabsTrigger>
            <TabsTrigger value="creditors">Acreedores</TabsTrigger>
            <TabsTrigger value="assets">Bienes</TabsTrigger>
            <TabsTrigger value="documents">Documentos</TabsTrigger>
          </TabsList>

          <TabsContent value="debtor" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Información del Deudor</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Nombre Completo
                    </p>
                    <p className="text-sm">{caseData?.debtor?.name || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Documento
                    </p>
                    <p className="text-sm">
                      {caseData?.debtor?.idNumber || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Tipo de Documento
                    </p>
                    <p className="text-sm">
                      {caseData?.debtor?.idType || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Teléfono
                    </p>
                    <p className="text-sm">
                      {caseData?.debtor?.phone || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Email</p>
                    <p className="text-sm">
                      {caseData?.debtor?.email || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Ciudad</p>
                    <p className="text-sm">{caseData?.debtor?.city || 'N/A'}</p>
                  </div>
                </div>
                <Separator />
                <div>
                  <p className="text-sm font-medium text-gray-600">Dirección</p>
                  <p className="text-sm">
                    {caseData?.debtor?.address || 'N/A'}
                  </p>
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Ingresos Mensuales
                    </p>
                    <p className="text-sm">
                      ${(caseData?.debtor?.monthlyIncome || 0).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Gastos Mensuales
                    </p>
                    <p className="text-sm">
                      $
                      {(
                        caseData?.debtor?.monthlyExpenses || 0
                      ).toLocaleString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="case-details" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Detalles del Caso</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Número de Caso
                    </p>
                    <p className="text-sm">{caseData?.caseNumber || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Tipo de Caso
                    </p>
                    <p className="text-sm">{caseData?.type || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Estado</p>
                    <Badge variant="default">{caseData?.status || 'N/A'}</Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Monto Total
                    </p>
                    <p className="text-sm">
                      ${(caseData?.totalDebt || 0).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Número de Acreedores
                    </p>
                    <p className="text-sm">{caseData?.creditors || 0}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Fecha de Creación
                    </p>
                    <p className="text-sm">
                      {caseData?.createdDate
                        ? new Date(caseData.createdDate).toLocaleDateString(
                            'es-CO',
                          )
                        : 'N/A'}
                    </p>
                  </div>
                </div>
                <Separator />
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Causas de Insolvencia
                  </p>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {caseData?.causes?.map((cause) => (
                      <Badge key={cause} variant="secondary">
                        {cause}
                      </Badge>
                    )) || (
                      <span className="text-sm text-gray-500">
                        No hay causas registradas
                      </span>
                    )}
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Trámite</p>
                  <p className="text-sm">
                    {caseData?.tramite || 'Sin especificar'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="legal-process" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Proceso Legal</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Número de Juzgado
                    </p>
                    <p className="text-sm">{caseData?.courtNumber || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Ciudad</p>
                    <p className="text-sm">{caseData?.city || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Tipo de Proceso
                    </p>
                    <p className="text-sm">{caseData?.processType || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Demandante
                    </p>
                    <p className="text-sm">{caseData?.plaintiff || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Número de Expediente
                    </p>
                    <p className="text-sm">
                      {caseData?.judicialFileNumber || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Abogado</p>
                    <p className="text-sm">{caseData?.attorney || 'N/A'}</p>
                  </div>
                </div>
                <Separator />
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Fecha de Radicación
                    </p>
                    <p className="text-sm">
                      {caseData?.filingDate
                        ? new Date(caseData.filingDate).toLocaleDateString(
                            'es-CO',
                          )
                        : 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Fecha de Admisión
                    </p>
                    <p className="text-sm">
                      {caseData?.admissionDate
                        ? new Date(caseData.admissionDate).toLocaleDateString(
                            'es-CO',
                          )
                        : 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Primera Audiencia
                    </p>
                    <p className="text-sm">
                      {caseData?.firstHearingDate
                        ? `${new Date(caseData.firstHearingDate).toLocaleDateString('es-CO')} ${caseData.firstHearingTime ? `a las ${caseData.firstHearingTime}` : ''}`
                        : 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Operador Designado
                    </p>
                    <p className="text-sm">
                      {caseData?.designatedOperator || 'N/A'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="creditors" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Acreedores</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {creditors.length > 0 ? (
                    creditors.map((creditor) => (
                      <div
                        key={creditor.name}
                        className="flex items-center justify-between rounded-lg border p-4"
                      >
                        <div>
                          <p className="font-medium">{creditor.name}</p>
                          <p className="text-sm text-gray-600">
                            {creditor.type}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">
                            ${creditor.amount.toLocaleString()}
                          </p>
                          <Badge variant="secondary">
                            {creditor.interestRate}%
                          </Badge>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="py-8 text-center text-gray-500">
                      No hay acreedores registrados
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="assets" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Bienes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="py-8 text-center text-gray-500">
                    No hay bienes registrados
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Documentos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="py-8 text-center text-gray-500">
                    No hay documentos registrados
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
