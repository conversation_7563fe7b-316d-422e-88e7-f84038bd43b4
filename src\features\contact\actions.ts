'use server';

import { revalidatePath } from 'next/cache';

import prisma from '@/lib/prisma';
import {
  viewCreditorsPermissionProcedure,
  createCreditorsPermissionProcedure,
  editCreditorsPermissionProcedure,
  deleteCreditorsPermissionProcedure,
} from '@/features/creditor/procedures';

import {
  contactSchema,
  contactWithCreditorSchema,
  updateContactSchema,
  deleteContactSchema,
  addContactToCreditorSchema,
  getAllContactsSchema,
  getContactByIdSchema,
} from './schemas';

export const getAllContacts = viewCreditorsPermissionProcedure
  .createServerAction()
  .output(getAllContactsSchema)
  .handler(async () => {
    return prisma.contact.findMany({
      include: {
        creditor: true,
      },
      orderBy: {
        name: 'asc',
      },
    });
  });

export const getContactById = viewCreditorsPermissionProcedure
  .createServerAction()
  .input(getContactByIdSchema)
  .output(contactWithCreditorSchema)
  .handler(async ({ input: id }) => {
    const contact = await prisma.contact.findUnique({
      where: { id },
      include: {
        creditor: true,
      },
    });

    if (!contact) {
      throw new Error('Contacto no encontrado');
    }

    return contact;
  });

export const addContactToCreditor = createCreditorsPermissionProcedure
  .createServerAction()
  .input(addContactToCreditorSchema)
  .output(contactSchema)
  .handler(async ({ input: { creditorId, ...contactData } }) => {
    const contact = await prisma.contact.create({
      data: {
        ...contactData,
        creditorId,
      },
    });

    revalidatePath('/creditors');

    return contact;
  });

export const updateContact = editCreditorsPermissionProcedure
  .createServerAction()
  .input(updateContactSchema)
  .output(contactSchema)
  .handler(async ({ input: { id, ...data } }) => {
    const contact = await prisma.contact.update({
      where: { id },
      data,
    });

    revalidatePath('/contacts');
    revalidatePath('/creditors');

    return contact;
  });

export const deleteContact = deleteCreditorsPermissionProcedure
  .createServerAction()
  .input(deleteContactSchema)
  .output(contactSchema)
  .handler(async ({ input: { id } }) => {
    const deletedContact = await prisma.contact.delete({
      where: { id },
    });

    revalidatePath('/contacts');
    revalidatePath('/creditors');

    return deletedContact;
  });
