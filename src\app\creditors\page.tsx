export const dynamic = 'force-dynamic';

import { PermissionGuard } from '@/components/auth/permission-guard';
import { getAllCreditors } from '@/features/creditor/actions';

import { DashboardHeader } from '../dashboard/components/dashboard-header';
import { CreditorManagement } from './components/creditor-management';

export default async function CreditorsPage() {
  const [creditors] = await getAllCreditors();

  return (
    <PermissionGuard
      permission="Ver acreedores"
      redirectTo="/dashboard"
      fallback={
        <div className="min-h-screen bg-gray-50">
          <DashboardHeader />
          <main className="container mx-auto px-4 py-6">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Acceso Denegado
              </h1>
              <p className="text-gray-600">
                No tienes permisos para ver acreedores.
              </p>
            </div>
          </main>
        </div>
      }
    >
      <div className="min-h-screen bg-gray-50">
        <DashboardHeader />

        <main className="container mx-auto px-4 py-6">
          <CreditorManagement creditors={creditors || []} />
        </main>
      </div>
    </PermissionGuard>
  );
}
