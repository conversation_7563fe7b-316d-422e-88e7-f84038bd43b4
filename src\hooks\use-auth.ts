'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useServerAction } from 'zsa-react';

import {
  validateSession,
  logoutUser,
  switchDatabase,
} from '@/features/auth/actions';
import type { ValidateSessionResult } from '@/features/auth/schemas';

type AuthUser = NonNullable<ValidateSessionResult['user']>;

interface UseAuthReturn {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  logout: () => Promise<void>;
  switchDatabase: () => Promise<void>;
  checkPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  refreshSession: () => Promise<void>;
}

export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const { execute: executeLogout } = useServerAction(logoutUser, {
    onSuccess: () => {
      setUser(null);
      router.push('/');
    },
  });

  const { execute: executeSwitchDatabase } = useServerAction(switchDatabase, {
    onSuccess: () => {
      setUser(null);
      router.push('/');
    },
  });

  const refreshSession = useCallback(async () => {
    try {
      setIsLoading(true);
      const [result] = await validateSession();

      if (result?.valid && result.user) {
        setUser(result.user);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Session refresh failed:', error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    refreshSession();
  }, [refreshSession]);

  const logout = useCallback(async () => {
    await executeLogout();
  }, [executeLogout]);

  const switchDatabaseHandler = useCallback(async () => {
    await executeSwitchDatabase();
  }, [executeSwitchDatabase]);

  const checkPermission = useCallback(
    (permission: string): boolean => {
      if (!user) return false;
      return user.permissions.includes(permission);
    },
    [user],
  );

  const hasAnyPermission = useCallback(
    (permissions: string[]): boolean => {
      if (!user) return false;
      return permissions.some((permission) =>
        user.permissions.includes(permission),
      );
    },
    [user],
  );

  const hasAllPermissions = useCallback(
    (permissions: string[]): boolean => {
      if (!user) return false;
      return permissions.every((permission) =>
        user.permissions.includes(permission),
      );
    },
    [user],
  );

  return {
    user,
    isAuthenticated: !!user,
    isLoading,
    logout,
    switchDatabase: switchDatabaseHandler,
    checkPermission,
    hasAnyPermission,
    hasAllPermissions,
    refreshSession,
  };
}
