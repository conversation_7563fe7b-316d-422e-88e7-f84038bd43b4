import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { jwtVerify } from 'jose';

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-change-in-production',
);

async function verifyToken(token: string) {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return {
      id: payload.id as string,
      name: payload.name as string,
      email: payload.email as string,
      roleId: payload.roleId as string,
      roleName: payload.roleName as string,
      permissions: payload.permissions as string[],
      database: payload.database as string,
      lastLogin: new Date(payload.lastLogin as string),
      iat: payload.iat as number,
      exp: payload.exp as number,
    };
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Get access token from cookies
  const accessToken = request.cookies.get('access-token')?.value;

  // Handle public routes (login and root)
  if (pathname === '/' || pathname === '/login') {
    if (accessToken) {
      // If user is authenticated, verify token and redirect to dashboard
      const user = await verifyToken(accessToken);
      if (user?.permissions?.length) {
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }
    }
    // If not authenticated or invalid token, allow access to login/root
    return NextResponse.next();
  }

  if (!accessToken) {
    // Redirect to login if no token
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Verify token
  const user = await verifyToken(accessToken);

  if (!user) {
    // Redirect to login if token is invalid
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Check if user is active
  if (!user.permissions || user.permissions.length === 0) {
    // Redirect to login if user has no permissions
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('error', 'no-permissions');
    return NextResponse.redirect(loginUrl);
  }

  // Allow access to all routes for authenticated users
  // Permission-based UI rendering will be handled by components

  // Add user info to headers for server components
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-user-id', user.id);
  requestHeaders.set('x-user-email', user.email);
  requestHeaders.set('x-user-role', user.roleName);
  requestHeaders.set('x-user-permissions', JSON.stringify(user.permissions));
  requestHeaders.set('x-user-database', user.database);

  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images).*)',
  ],
};
