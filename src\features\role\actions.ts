'use server';

import { revalidateTag } from 'next/cache';
import { z } from 'zod';

import prisma from '@/lib/prisma';
import {
  viewRolesPermissionProcedure,
  createRolesPermissionProcedure,
  editRolesPermissionProcedure,
  deleteRolesPermissionProcedure,
} from './procedures';

import { createRoleSchema, updateRoleSchema, roleSchema } from './schemas';

export const getAllRoles = viewRolesPermissionProcedure
  .createServerAction()
  .output(z.array(roleSchema))
  .handler(async () => {
    const roles = await prisma.role.findMany({
      include: {
        users: true,
        permissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    // Transform the data to match the expected schema
    return roles.map((role) => ({
      ...role,
      permissions: role.permissions.map((rp) => rp.permission.name),
    }));
  });

export const createRole = createRolesPermissionProcedure
  .createServerAction()
  .input(createRoleSchema)
  .output(roleSchema)
  .handler(async ({ input: { permissions, ...roleData } }) => {
    // Create the role first
    const newRole = await prisma.role.create({
      data: roleData,
    });

    // Find the permissions by name and create the relationships
    const permissionRecords = await prisma.permission.findMany({
      where: {
        name: {
          in: permissions,
        },
      },
    });

    // Create role-permission relationships
    await prisma.rolePermission.createMany({
      data: permissionRecords.map((permission) => ({
        roleId: newRole.id,
        permissionId: permission.id,
      })),
    });

    // Fetch the complete role with permissions and users
    const roleWithRelations = await prisma.role.findUnique({
      where: { id: newRole.id },
      include: {
        users: true,
        permissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    revalidateTag('roles');

    // Transform the data to match the expected schema
    return {
      ...roleWithRelations!,
      permissions: roleWithRelations!.permissions.map(
        (rp) => rp.permission.name,
      ),
    };
  });

export const updateRole = editRolesPermissionProcedure
  .createServerAction()
  .input(updateRoleSchema)
  .output(roleSchema)
  .handler(async ({ input: { id, permissions, ...roleData } }) => {
    if (!id) {
      throw new Error('Role ID is required');
    }

    // Update the role basic data
    await prisma.role.update({
      where: { id },
      data: roleData,
    });

    // If permissions are provided, update the role-permission relationships
    if (permissions) {
      // Delete existing role-permission relationships
      await prisma.rolePermission.deleteMany({
        where: { roleId: id },
      });

      // Find the permissions by name
      const permissionRecords = await prisma.permission.findMany({
        where: {
          name: {
            in: permissions,
          },
        },
      });

      // Create new role-permission relationships
      await prisma.rolePermission.createMany({
        data: permissionRecords.map((permission) => ({
          roleId: id,
          permissionId: permission.id,
        })),
      });
    }

    // Fetch the complete role with permissions and users
    const roleWithRelations = await prisma.role.findUnique({
      where: { id },
      include: {
        users: true,
        permissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    revalidateTag('roles');

    // Transform the data to match the expected schema
    return {
      ...roleWithRelations!,
      permissions: roleWithRelations!.permissions.map(
        (rp) => rp.permission.name,
      ),
    };
  });

export const deleteRole = deleteRolesPermissionProcedure
  .createServerAction()
  .input(z.string())
  .output(roleSchema)
  .handler(async ({ input: id }) => {
    const usersWithRole = await prisma.user.count({
      where: { roleId: id },
    });

    if (usersWithRole > 0) {
      throw new Error(
        'No se puede eliminar el rol porque tiene usuarios asignados',
      );
    }

    // First get the role with its relationships before deleting
    const roleToDelete = await prisma.role.findUnique({
      where: { id },
      include: {
        users: true,
        permissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    if (!roleToDelete) {
      throw new Error('Rol no encontrado');
    }

    // Delete the role (this will cascade delete the role-permission relationships)
    await prisma.role.delete({
      where: { id },
    });

    revalidateTag('roles');

    // Transform the data to match the expected schema
    return {
      ...roleToDelete,
      permissions: roleToDelete.permissions.map((rp) => rp.permission.name),
    };
  });
