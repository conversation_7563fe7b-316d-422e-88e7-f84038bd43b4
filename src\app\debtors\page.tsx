import { Users, DollarSign, Briefcase, TrendingUp } from 'lucide-react';

export const dynamic = 'force-dynamic';

import { PermissionGuard } from '@/components/auth/permission-guard';
import { Card, CardContent } from '@/components/ui/card';
import { getAllDebtors } from '@/features/debtor/actions';
import { formatCurrency } from '@/lib/utils';

import { DashboardHeader } from '../dashboard/components/dashboard-header';
import { DebtorManagement } from './components/debtor-management';

export default async function DebtorsPage() {
  const [debtors, error] = await getAllDebtors();

  if (error) {
    throw new Error('Error al obtener los deudores');
  }

  const totalDebtors = debtors.length;
  const activeDebtors = debtors.filter((d) => d.status === 'En proceso').length;
  const totalDebt = debtors.reduce((sum, d) => sum + d.totalDebt, 0);
  const totalCases = debtors.reduce((sum, d) => sum + d.activeCases, 0);

  return (
    <PermissionGuard
      permission="Ver deudores"
      redirectTo="/dashboard"
      fallback={
        <div className="min-h-screen bg-gray-50">
          <DashboardHeader />
          <main className="container mx-auto px-4 py-6">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Acceso Denegado
              </h1>
              <p className="text-gray-600">
                No tienes permisos para ver deudores.
              </p>
            </div>
          </main>
        </div>
      }
    >
      <div className="min-h-screen bg-gray-50">
        <DashboardHeader />

        <main className="container mx-auto px-4 py-6">
          <div className="space-y-6">
            <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Total Deudores
                      </p>
                      <p className="text-2xl font-bold">{totalDebtors}</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Deudores Activos
                      </p>
                      <p className="text-2xl font-bold">{activeDebtors}</p>
                    </div>
                    <Briefcase className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Deuda Total
                      </p>
                      <p className="text-2xl font-bold">
                        {formatCurrency(totalDebt)}
                      </p>
                    </div>
                    <DollarSign className="h-8 w-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Casos Activos
                      </p>
                      <p className="text-2xl font-bold">{totalCases}</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            <DebtorManagement debtors={debtors} />
          </div>
        </main>
      </div>
    </PermissionGuard>
  );
}
