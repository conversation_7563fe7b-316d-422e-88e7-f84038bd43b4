'use client';

import { useState, useEffect } from 'react';
import { Wand2, Check, FileDown, Loader2 } from 'lucide-react';
import { useServerAction } from 'zsa-react';
import { toast } from 'sonner';

import { useAuth } from '@/hooks/use-auth';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { getAllCases } from '@/features/case/actions';
import { batchGenerateDocuments } from '@/features/document/actions';

interface DocumentGenerationProps {
  onDocumentsGenerated: () => void;
}

export function DocumentGeneration({
  onDocumentsGenerated,
}: Readonly<DocumentGenerationProps>) {
  const { checkPermission } = useAuth();
  const canGenerateDocuments = checkPermission('Generar documentos');
  const [selectedCaseForGeneration, setSelectedCaseForGeneration] =
    useState('');
  const [generationResult, setGenerationResult] = useState<{
    caseId: string;
    caseNumber: string;
    folderId: string;
    folderUrl: string;
    documents: Array<{
      templateId: string;
      templateName: string;
      documentId: string;
      documentName: string;
      documentUrl: string;
      status: 'success' | 'error';
      error?: string;
    }>;
    generatedAt: Date;
    totalDocuments: number;
    successfulDocuments: number;
    failedDocuments: number;
  } | null>(null);

  const [cases, setCases] = useState<
    Array<{
      id: string;
      caseNumber: string;
      debtorName: string;
      type: string;
      status: string;
    }>
  >([]);

  const { execute: loadCases } = useServerAction(getAllCases, {
    onSuccess: ({ data }) => {
      setCases(
        data.map((c) => ({
          id: c.id,
          caseNumber: c.caseNumber,
          debtorName: c.debtorName,
          type: c.type,
          status: c.status,
        })),
      );
    },
  });

  const { execute: executeBatchGeneration, isPending: isGenerating } =
    useServerAction(batchGenerateDocuments, {
      onSuccess: ({ data }) => {
        if (data?.data) {
          setGenerationResult(data.data);
          onDocumentsGenerated();
          toast.success('Documentos generados exitosamente');
        }
      },
      onError: ({ err }) => {
        console.error('Error in batch generation:', err);
        toast.error(
          'Error al generar documentos: ' +
            (err.message || 'Error desconocido'),
        );
      },
    });

  const handleBatchGeneration = () => {
    if (!selectedCaseForGeneration) return;

    setGenerationResult(null);

    executeBatchGeneration({
      caseId: selectedCaseForGeneration,
      outputFormat: 'docx',
    });
  };

  useEffect(() => {
    loadCases();
  }, [loadCases]);

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wand2 className="h-5 w-5" />
          Generación de Documentos
        </CardTitle>
        <CardDescription>
          Genera todos los documentos de plantilla para un caso específico
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-[1fr_auto] lg:items-end">
          <div className="space-y-2">
            <Label htmlFor="case-select">Seleccionar Caso</Label>
            <Select
              value={selectedCaseForGeneration}
              onValueChange={setSelectedCaseForGeneration}
            >
              <SelectTrigger id="case-select">
                <SelectValue placeholder="Selecciona un caso..." />
              </SelectTrigger>
              <SelectContent>
                {cases.map((caseItem) => (
                  <SelectItem key={caseItem.id} value={caseItem.id}>
                    <div className="flex flex-col">
                      <span className="font-medium">{caseItem.caseNumber}</span>
                      <span className="text-muted-foreground text-sm">
                        {caseItem.debtorName}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button
            onClick={handleBatchGeneration}
            disabled={
              !selectedCaseForGeneration ||
              isGenerating ||
              !canGenerateDocuments
            }
            className="w-full lg:w-auto"
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generando...
              </>
            ) : (
              <>
                <FileDown className="mr-2 h-4 w-4" />
                Generar Documentos
              </>
            )}
          </Button>
        </div>

        {generationResult && (
          <div className="mt-6 rounded-lg border bg-green-50 p-4">
            <div className="flex items-center gap-2 text-green-800">
              <Check className="h-5 w-5" />
              <h3 className="font-semibold">Documentos Generados</h3>
            </div>
            <div className="mt-2 space-y-2">
              <p className="text-sm text-green-700">
                Se generaron {generationResult.successfulDocuments} de{' '}
                {generationResult.totalDocuments} documentos para el caso{' '}
                <strong>{generationResult.caseNumber}</strong>
              </p>
              <div className="flex flex-wrap gap-2">
                {generationResult.documents.map((doc) => (
                  <Badge
                    key={doc.documentId}
                    variant={
                      doc.status === 'success' ? 'default' : 'destructive'
                    }
                  >
                    {doc.templateName}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
