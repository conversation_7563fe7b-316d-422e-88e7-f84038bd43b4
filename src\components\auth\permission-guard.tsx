'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

import { validateSession } from '@/features/auth/actions';

interface PermissionGuardProps {
  permission: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

export function PermissionGuard({
  permission,
  children,
  fallback = null,
  redirectTo,
}: Readonly<PermissionGuardProps>) {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function checkPermission() {
      try {
        const [result] = await validateSession();

        if (!result?.valid || !result.user) {
          setHasPermission(false);
          if (redirectTo) {
            router.push(redirectTo);
          }
          return;
        }

        const userHasPermission = result.user.permissions.includes(permission);

        setHasPermission(userHasPermission);

        if (!userHasPermission && redirectTo) {
          router.push(redirectTo);
        }
      } catch (error) {
        console.error('Permission check failed:', error);
        setHasPermission(false);
        if (redirectTo) {
          router.push(redirectTo);
        }
      } finally {
        setIsLoading(false);
      }
    }

    checkPermission();
  }, [permission, redirectTo, router]);

  if (isLoading) {
    return <div>Verificando permisos...</div>;
  }

  if (!hasPermission) {
    return fallback;
  }

  return <>{children}</>;
}
