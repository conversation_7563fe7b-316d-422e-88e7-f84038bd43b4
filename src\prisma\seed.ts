import { PrismaClient, Role, User, Debtor, Creditor } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  const permissions = await createPermissions();

  const roles = await createRoles(permissions);

  const users = await createUsers(roles);

  const creditors = await createCreditors();

  const debtors = await createDebtors();

  await createCases(debtors, users, creditors);

  await createNotifications(users);
}

async function createPermissions() {
  const permissionsData = [
    {
      name: 'Ver usuarios',
      category: 'Usuarios y Roles',
    },
    {
      name: 'Crear usuarios',
      category: 'Usuarios y Roles',
    },
    {
      name: 'Editar usuarios',
      category: 'Usuarios y Roles',
    },
    {
      name: 'Eliminar usuarios',
      category: 'Usuarios y Roles',
    },
    {
      name: 'Ver roles',
      category: 'Usuarios y Roles',
    },
    {
      name: 'Crear roles',
      category: 'Usuarios y Roles',
    },
    {
      name: 'Editar roles',
      category: 'Usuarios y Roles',
    },
    {
      name: 'Eliminar roles',
      category: 'Usuarios y Roles',
    },
    {
      name: 'Ver casos',
      category: 'Casos',
    },
    {
      name: 'Crear casos',
      category: 'Casos',
    },
    {
      name: 'Editar casos',
      category: 'Casos',
    },
    {
      name: 'Eliminar casos',
      category: 'Casos',
    },
    {
      name: 'Ver acreedores',
      category: 'Acreedores',
    },
    {
      name: 'Crear acreedores',
      category: 'Acreedores',
    },
    {
      name: 'Editar acreedores',
      category: 'Acreedores',
    },
    {
      name: 'Eliminar acreedores',
      category: 'Acreedores',
    },
    {
      name: 'Ver deudores',
      category: 'Deudores',
    },
    {
      name: 'Crear deudores',
      category: 'Deudores',
    },
    {
      name: 'Editar deudores',
      category: 'Deudores',
    },
    {
      name: 'Eliminar deudores',
      category: 'Deudores',
    },
    {
      name: 'Ver documentos',
      category: 'Documentos',
    },
    {
      name: 'Generar documentos',
      category: 'Documentos',
    },
    {
      name: 'Ver notificaciones',
      category: 'Notificaciones',
    },
    {
      name: 'Editar notificaciones',
      category: 'Notificaciones',
    },
    {
      name: 'Gestionar notificaciones',
      category: 'Notificaciones',
    },
  ];

  const createdPermissions = [];
  for (const permissionData of permissionsData) {
    const permission = await prisma.permission.upsert({
      where: { name: permissionData.name },
      update: {},
      create: permissionData,
    });
    createdPermissions.push(permission);
  }

  return createdPermissions;
}

async function createRoles(
  permissions: Array<{
    id: string;
    name: string;
    category: string;
  }>,
) {
  const findPermission = (name: string) =>
    permissions.find((p) => p.name === name);

  const adminRole = await prisma.role.upsert({
    where: { name: 'Administrador' },
    update: {},
    create: {
      name: 'Administrador',
      description: 'Acceso completo al sistema',
      color: 'bg-red-100 text-red-800',
    },
  });

  const adminPermissions = [
    'Ver usuarios',
    'Crear usuarios',
    'Editar usuarios',
    'Eliminar usuarios',
    'Ver casos',
    'Crear casos',
    'Editar casos',
    'Eliminar casos',
    'Ver acreedores',
    'Crear acreedores',
    'Editar acreedores',
    'Eliminar acreedores',
    'Ver deudores',
    'Crear deudores',
    'Editar deudores',
    'Eliminar deudores',
    'Ver contactos',
    'Ver documentos',
    'Generar documentos',
    'Ver roles',
    'Crear roles',
    'Editar roles',
    'Eliminar roles',
    'Ver notificaciones',
    'Editar notificaciones',
    'Gestionar notificaciones',
  ];

  for (const permissionName of adminPermissions) {
    const permission = findPermission(permissionName);
    if (permission) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: adminRole.id,
            permissionId: permission.id,
          },
        },
        update: {},
        create: {
          roleId: adminRole.id,
          permissionId: permission.id,
        },
      });
    }
  }

  const operatorRole = await prisma.role.upsert({
    where: { name: 'Operadora de Insolvencia' },
    update: {},
    create: {
      name: 'Operadora de Insolvencia',
      description: 'Operador especializado en procesos de insolvencia',
      color: 'bg-purple-100 text-purple-800',
    },
  });

  const operatorPermissions = [
    'Ver casos',
    'Crear casos',
    'Editar casos',
    'Eliminar casos',
    'Ver acreedores',
    'Crear acreedores',
    'Editar acreedores',
    'Ver deudores',
    'Crear deudores',
    'Editar deudores',
    'Ver documentos',
    'Generar documentos',
    'Ver notificaciones',
  ];

  for (const permissionName of operatorPermissions) {
    const permission = findPermission(permissionName);
    if (permission) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: operatorRole.id,
            permissionId: permission.id,
          },
        },
        update: {},
        create: {
          roleId: operatorRole.id,
          permissionId: permission.id,
        },
      });
    }
  }

  const lawyerRole = await prisma.role.upsert({
    where: { name: 'Abogado' },
    update: {},
    create: {
      name: 'Abogado',
      description: 'Gestión de casos y audiencias',
      color: 'bg-blue-100 text-blue-800',
    },
  });

  const lawyerPermissions = [
    'Ver casos',
    'Crear casos',
    'Editar casos',
    'Ver acreedores',
    'Ver deudores',
    'Ver documentos',
    'Ver notificaciones',
  ];

  for (const permissionName of lawyerPermissions) {
    const permission = findPermission(permissionName);
    if (permission) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: lawyerRole.id,
            permissionId: permission.id,
          },
        },
        update: {},
        create: {
          roleId: lawyerRole.id,
          permissionId: permission.id,
        },
      });
    }
  }

  const assistantRole = await prisma.role.upsert({
    where: { name: 'Asistente Legal' },
    update: {},
    create: {
      name: 'Asistente Legal',
      description: 'Apoyo en gestión legal y documental',
      color: 'bg-green-100 text-green-800',
    },
  });

  const assistantPermissions = [
    'Ver casos',
    'Ver acreedores',
    'Ver deudores',
    'Ver documentos',
    'Ver notificaciones',
  ];

  for (const permissionName of assistantPermissions) {
    const permission = findPermission(permissionName);
    if (permission) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: assistantRole.id,
            permissionId: permission.id,
          },
        },
        update: {},
        create: {
          roleId: assistantRole.id,
          permissionId: permission.id,
        },
      });
    }
  }

  const secretaryRole = await prisma.role.upsert({
    where: { name: 'Secretaria' },
    update: {},
    create: {
      name: 'Secretaria',
      description: 'Apoyo administrativo',
      color: 'bg-yellow-100 text-yellow-800',
    },
  });

  const secretaryPermissions = ['Ver documentos', 'Ver casos'];

  for (const permissionName of secretaryPermissions) {
    const permission = findPermission(permissionName);
    if (permission) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: secretaryRole.id,
            permissionId: permission.id,
          },
        },
        update: {},
        create: {
          roleId: secretaryRole.id,
          permissionId: permission.id,
        },
      });
    }
  }

  return { adminRole, operatorRole, lawyerRole, assistantRole, secretaryRole };
}

interface Roles {
  adminRole: Role;
  lawyerRole: Role;
  assistantRole: Role;
  operatorRole: Role;
  secretaryRole: Role;
}

async function createUsers(roles: Roles) {
  const hashedPassword = await bcrypt.hash('123456', 10);

  const beatriz = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Beatriz Helena Malavera López',
      email: '<EMAIL>',
      password: hashedPassword,
      lastLogin: new Date('2025-01-29T08:30:00'),
      professionalCard: '194.548',
      phone: '3101234567',
      address: 'Calle 123 #45-67, Bogotá',
      createdDate: new Date('2025-01-01'),
      roleId: roles.adminRole.id,
    },
  });

  const sofia = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Sofía Alejandra Torres Ramírez',
      email: '<EMAIL>',
      password: hashedPassword,
      lastLogin: new Date('2025-01-29T09:15:00'),
      professionalCard: '198.765',
      phone: '3187654321',
      address: 'Carrera 11 #85-23, Bogotá',
      createdDate: new Date('2025-01-01'),
      roleId: roles.operatorRole.id,
    },
  });

  const maximiliano = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Maximiliano Jaramillo Jaramillo',
      email: '<EMAIL>',
      password: hashedPassword,
      lastLogin: new Date('2025-01-29T07:45:00'),
      professionalCard: '156.789',
      phone: '3202345678',
      address: 'Carrera 45 #12-34, Bogotá',
      createdDate: new Date('2025-01-01'),
      roleId: roles.lawyerRole.id,
    },
  });

  const carlos = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Carlos Andrés Rodríguez',
      email: '<EMAIL>',
      password: hashedPassword,
      lastLogin: new Date('2025-01-28T16:20:00'),
      professionalCard: 'N/A',
      phone: '3153456789',
      address: 'Avenida 34 #56-78, Bogotá',
      createdDate: new Date('2025-01-01'),
      roleId: roles.assistantRole.id,
    },
  });

  const maria = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'María Fernanda González',
      email: '<EMAIL>',
      password: hashedPassword,
      lastLogin: new Date('2025-01-25T14:15:00'),
      professionalCard: 'N/A',
      phone: '3164567890',
      address: 'Calle 89 #12-34, Bogotá',
      createdDate: new Date('2025-01-01'),
      roleId: roles.secretaryRole.id,
    },
  });

  return { beatriz, sofia, maximiliano, carlos, maria };
}

interface Debtors {
  maria: Debtor;
  carlos: Debtor;
  ana: Debtor;
}

interface Users {
  beatriz: User;
  sofia: User;
  maximiliano: User;
  carlos: User;
  maria: User;
}

interface Creditors {
  bancolombia: Creditor;
  coopvalle: Creditor;
  tarjetasxyz: Creditor;
  finpopular: Creditor;
}

async function createCreditors() {
  const bancolombia = await prisma.creditor.upsert({
    where: { nit: '860.002.964-4' },
    update: {},
    create: {
      name: 'Banco Nacional de Colombia',
      type: 'FINANCIAL',
      email: '<EMAIL>',
      phone: '+57 1 234-5678',
      address: 'Carrera 7 #32-16, Bogotá',
      status: 'Activo',
      representative: 'María García',
      nit: '860.002.964-4',
      city: 'Bogotá',
      department: 'Cundinamarca',
      activeCases: 12,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  const coopvalle = await prisma.creditor.upsert({
    where: { nit: '890.300.567-8' },
    update: {},
    create: {
      name: 'Cooperativa Financiera del Valle',
      type: 'Cooperativa',
      email: '<EMAIL>',
      phone: '+57 2 345-6789',
      address: 'Avenida 6N #25-45, Cali',
      status: 'Activo',
      representative: 'Carlos Rodríguez',
      nit: '890.300.567-8',
      city: 'Cali',
      department: 'Valle del Cauca',
      activeCases: 8,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  const tarjetasxyz = await prisma.creditor.upsert({
    where: { nit: '900.123.456-7' },
    update: {},
    create: {
      name: 'Tarjetas de Crédito XYZ',
      type: 'FINANCIAL',
      email: '<EMAIL>',
      phone: '+57 4 456-7890',
      address: 'Calle 50 #45-30, Medellín',
      status: 'Activo',
      representative: 'Ana Martínez',
      nit: '900.123.456-7',
      city: 'Medellín',
      department: 'Antioquia',
      activeCases: 15,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  const finpopular = await prisma.creditor.upsert({
    where: { nit: '800.456.789-1' },
    update: {},
    create: {
      name: 'Financiera Popular',
      type: 'FINANCIAL',
      email: '<EMAIL>',
      phone: '+57 5 567-8901',
      address: 'Carrera 54 #72-15, Barranquilla',
      status: 'Inactivo',
      representative: 'Luis Fernández',
      nit: '800.456.789-1',
      city: 'Barranquilla',
      department: 'Atlántico',
      activeCases: 6,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  return { bancolombia, coopvalle, tarjetasxyz, finpopular };
}

async function createDebtors() {
  const maria = await prisma.debtor.upsert({
    where: { idNumber: '********' },
    update: {},
    create: {
      name: 'María González Pérez',
      idNumber: '********',
      idType: 'CC',
      email: '<EMAIL>',
      phone: '+57 ************',
      address: 'Calle 45 #12-34, Bogotá',
      city: 'Bogotá',
      department: 'Cundinamarca',
      birthDate: new Date('1985-05-15'),
      maritalStatus: 'CASADO',
      occupation: 'Empleada',
      monthlyIncome: 2500000,
      monthlyExpenses: 2200000,
      dependents: 2,
      educationLevel: 'UNIVERSITARIO',
      totalDebt: ********,
      status: 'En proceso',
      emergencyContact: 'Pedro González',
      emergencyPhone: '31********',
      bankAccount: '********',
      bankName: 'Bancolombia',
      accountType: 'AHORROS',
      activeCases: 1,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  const carlos = await prisma.debtor.upsert({
    where: { idNumber: '********' },
    update: {},
    create: {
      name: 'Carlos Rodríguez Silva',
      idNumber: '********',
      idType: 'CC',
      email: '<EMAIL>',
      phone: '+57 ************',
      address: 'Carrera 15 #67-89, Medellín',
      city: 'Medellín',
      department: 'Antioquia',
      birthDate: new Date('1978-08-20'),
      maritalStatus: 'SOLTERO',
      occupation: 'Independiente',
      monthlyIncome: 1800000,
      monthlyExpenses: 1600000,
      dependents: 0,
      educationLevel: 'POSTGRADO',
      totalDebt: ********,
      status: 'Audiencia programada',
      emergencyContact: 'Ana Rodríguez',
      emergencyPhone: '**********',
      bankAccount: '********',
      bankName: 'Davivienda',
      accountType: 'CORRIENTE',
      activeCases: 1,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  const ana = await prisma.debtor.upsert({
    where: { idNumber: '********' },
    update: {},
    create: {
      name: 'Ana Martínez López',
      idNumber: '********',
      idType: 'CC',
      email: '<EMAIL>',
      phone: '+57 ************',
      address: 'Avenida 80 #45-12, Cali',
      city: 'Cali',
      department: 'Valle del Cauca',
      birthDate: new Date('1990-03-10'),
      maritalStatus: 'UNION_LIBRE',
      occupation: 'Pensionada',
      monthlyIncome: 1200000,
      monthlyExpenses: 1000000,
      dependents: 1,
      educationLevel: 'TECNICO',
      totalDebt: ********,
      status: 'Documentos pendientes',
      bankAccount: '********',
      bankName: 'Banco de Bogotá',
      accountType: 'AHORROS',
      activeCases: 1,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  return { maria, carlos, ana };
}

async function createCases(
  debtors: Debtors,
  users: Users,
  creditors: Creditors,
) {
  const case1 = await prisma.case.create({
    data: {
      caseNumber: 'INS-2025-001',
      debtorName: 'María González Pérez',
      type: 'INSOLVENCY',
      status: 'NEGOTIATION',
      totalDebt: ********,
      creditors: 5,
      createdDate: new Date('2025-01-15'),
      hearingDate: new Date('2025-02-14'),
      phase: 'En negociación',
      debtorId: debtors.maria.id,
      operatorId: users.sofia.id,
      tramite: 'Insolvencia de Persona Natural No Comerciante',
      filingDate: new Date('2025-01-10'),
      debtorIdNumber: '********',
      convened: 'Cooperativa Financiera',
      attorney: 'Dr. Juan Carlos Pérez',
      owedCapital: ********,
      designatedOperator: 'Sofía Alejandra Torres Ramírez',
      designationDate: new Date('2025-01-12'),
      positionAcceptanceDate: new Date('2025-01-13'),
      inadmissionDate: null,
      admissionDate: new Date('2025-01-14'),
      firstHearingDate: new Date('2025-02-14'),
      firstHearingTime: '10:00',
      rejection: false,
      withdrawal: false,
      hasLegalProcesses: true,
      courtNumber: 'Juzgado 15 Civil del Circuito',
      city: 'Bogotá',
      processType: 'EJECUTIVO',
      plaintiff: 'Banco Nacional de Colombia',
      judicialFileNumber: '2024-00123',
      suspensionDate: new Date('2025-01-16'),
      resultDeliveryDate: null,
      resultType: null,
      resultDate: null,
      siccacNumber: null,
      riskCenterCommunication: true,
    },
  });

  const case2 = await prisma.case.create({
    data: {
      caseNumber: 'CON-2025-002',
      debtorName: 'Carlos Rodríguez Silva',
      type: 'CONCILIATION',
      status: 'HEARING_SCHEDULED',
      totalDebt: ********,
      creditors: 3,
      createdDate: new Date('2025-01-10'),
      hearingDate: new Date('2025-02-09'),
      phase: 'Audiencia programada',
      debtorId: debtors.carlos.id,
      operatorId: users.maximiliano.id,
      tramite: 'Conciliación Extrajudicial',
      filingDate: new Date('2025-01-08'),
      debtorIdNumber: '********',
      convened: 'Banco Nacional',
      attorney: 'Dra. Ana María López',
      owedCapital: 26000000,
      designatedOperator: 'Maximiliano Jaramillo Pérez',
      designationDate: new Date('2025-01-09'),
      positionAcceptanceDate: new Date('2025-01-10'),
      inadmissionDate: null,
      admissionDate: new Date('2025-01-11'),
      firstHearingDate: new Date('2025-02-09'),
      firstHearingTime: '14:30',
      rejection: false,
      withdrawal: false,
      hasLegalProcesses: false,
      courtNumber: null,
      city: 'Medellín',
      processType: null,
      plaintiff: null,
      judicialFileNumber: null,
      suspensionDate: null,
      resultDeliveryDate: null,
      resultType: null,
      resultDate: null,
      siccacNumber: null,
      riskCenterCommunication: false,
    },
  });

  const case3 = await prisma.case.create({
    data: {
      caseNumber: 'ACU-2025-003',
      debtorName: 'Ana Martínez López',
      type: 'SUPPORT_AGREEMENT',
      status: 'PENDING_DOCUMENTS',
      totalDebt: ********,
      creditors: 2,
      createdDate: new Date('2025-01-05'),
      hearingDate: null,
      phase: 'Documentos pendientes',
      debtorId: debtors.ana.id,
      operatorId: users.sofia.id,
      tramite: 'Acuerdo de Apoyo Económico',
      filingDate: new Date('2025-01-03'),
      debtorIdNumber: '********',
      convened: null,
      attorney: null,
      owedCapital: 14500000,
      designatedOperator: 'Sofía Alejandra Torres Ramírez',
      designationDate: new Date('2025-01-04'),
      positionAcceptanceDate: new Date('2025-01-05'),
      inadmissionDate: null,
      admissionDate: null,
      firstHearingDate: null,
      firstHearingTime: null,
      rejection: false,
      withdrawal: false,
      hasLegalProcesses: false,
      courtNumber: null,
      city: 'Cali',
      processType: null,
      plaintiff: null,
      judicialFileNumber: null,
      suspensionDate: null,
      resultDeliveryDate: null,
      resultType: null,
      resultDate: null,
      siccacNumber: null,
      riskCenterCommunication: false,
    },
  });

  const luis = await prisma.debtor.create({
    data: {
      name: 'Luis Fernando Castro',
      idNumber: '********',
      idType: 'CC',
      email: '<EMAIL>',
      phone: '+57 ************',
      address: 'Carrera 30 #25-90, Bogotá',
      city: 'Bogotá',
      department: 'Cundinamarca',
      birthDate: new Date('1975-12-01'),
      maritalStatus: 'CASADO',
      occupation: 'Empresario',
      monthlyIncome: 5000000,
      monthlyExpenses: 3500000,
      dependents: 3,
      educationLevel: 'POSTGRADO',
      totalDebt: ********,
      status: 'Acuerdo aprobado',
      emergencyContact: 'Carmen Castro',
      emergencyPhone: '**********',
      bankAccount: '********',
      bankName: 'BBVA',
      accountType: 'CORRIENTE',
      activeCases: 1,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  const case4 = await prisma.case.create({
    data: {
      caseNumber: 'INS-2025-004',
      debtorName: 'Luis Fernando Castro',
      type: 'INSOLVENCY',
      status: 'AGREEMENT_APPROVED',
      totalDebt: ********,
      creditors: 8,
      createdDate: new Date('2025-01-01'),
      hearingDate: new Date('2025-01-24'),
      phase: 'Acuerdo aprobado',
      debtorId: luis.id,
      operatorId: users.sofia.id,
      tramite: 'Insolvencia de Persona Natural No Comerciante',
      filingDate: new Date('2024-12-28'),
      debtorIdNumber: '********',
      convened: 'Entidad Financiera XYZ',
      attorney: 'Dr. Roberto Sánchez',
      owedCapital: ********,
      designatedOperator: 'Sofía Alejandra Torres Ramírez',
      designationDate: new Date('2024-12-30'),
      positionAcceptanceDate: new Date('2025-01-02'),
      inadmissionDate: null,
      admissionDate: new Date('2025-01-03'),
      firstHearingDate: new Date('2025-01-24'),
      firstHearingTime: '09:00',
      rejection: false,
      withdrawal: false,
      hasLegalProcesses: true,
      courtNumber: 'Juzgado 8 Civil del Circuito',
      city: 'Bogotá',
      processType: 'ORDINARIO',
      plaintiff: 'Cooperativa Financiera del Valle',
      judicialFileNumber: '2024-00456',
      suspensionDate: new Date('2025-01-05'),
      resultDeliveryDate: new Date('2025-01-25'),
      resultType: 'ACUERDO',
      resultDate: new Date('2025-01-24'),
      siccacNumber: 'SICCAC-2025-001',
      riskCenterCommunication: true,
    },
  });

  const cases = [case1, case2, case3, case4];
  const creditorsList = [
    creditors.bancolombia,
    creditors.coopvalle,
    creditors.tarjetasxyz,
    creditors.finpopular,
  ];

  for (const caseItem of cases) {
    const numDebts = Math.min(caseItem.creditors, 3);
    for (let i = 0; i < numDebts; i++) {
      const creditor = creditorsList[i % creditorsList.length];
      const debtAmount = Math.floor(
        caseItem.totalDebt.toNumber() / caseItem.creditors,
      );

      await prisma.debt.create({
        data: {
          amount: debtAmount + i * 1000000,
          interestRate: 1.5 + i * 0.5,
          type: 'PERSONAL',
          caseId: caseItem.id,
          creditorId: creditor.id,
          debtorId: caseItem.debtorId,
        },
      });
    }

    const assetValueBase = 80000000;
    const assetValueVariation = cases.indexOf(caseItem) * 10000000;
    await prisma.asset.create({
      data: {
        name: 'Bien Inmueble',
        type: 'INMUEBLE',
        value: assetValueBase + assetValueVariation,
        caseId: caseItem.id,
        debtorId: caseItem.debtorId,
      },
    });

    let documentTypeName: string;
    if (caseItem.type === 'INSOLVENCY') {
      documentTypeName = 'Insolvencia';
    } else if (caseItem.type === 'CONCILIATION') {
      documentTypeName = 'Conciliación';
    } else {
      documentTypeName = 'Acuerdo';
    }

    let caseNumberLower: string;
    const caseIndex = cases.indexOf(caseItem) + 1;
    if (caseItem.type === 'INSOLVENCY') {
      caseNumberLower = `ins-2025-00${caseIndex}`;
    } else if (caseItem.type === 'CONCILIATION') {
      caseNumberLower = 'con-2025-002';
    } else {
      caseNumberLower = `acu-2025-00${caseIndex}`;
    }

    await prisma.document.create({
      data: {
        name: `Solicitud de ${documentTypeName}`,
        type: 'LAWSUIT',
        status:
          caseItem.status === 'AGREEMENT_APPROVED' ? 'APPROVED' : 'PENDING',
        uploadDate: caseItem.createdDate,
        url: `/documents/${caseNumberLower}_solicitud.pdf`,
        caseId: caseItem.id,
      },
    });
  }
}

async function createNotifications(users: Users) {
  const case1 = await prisma.case.findFirst();

  const notifications = [
    {
      type: 'case_created',
      title: 'Nuevo caso creado',
      message: 'Se ha creado un nuevo caso de insolvencia',
      userId: users.beatriz.id,
      caseId: case1?.id,
    },
    {
      type: 'creditor_updated',
      title: 'Acreedor actualizado',
      message: 'Se ha actualizado la información de un acreedor',
      userId: users.beatriz.id,
    },
    {
      type: 'debtor_created',
      title: 'Nuevo deudor registrado',
      message: 'Se ha registrado un nuevo deudor en el sistema',
      userId: users.beatriz.id,
    },
    {
      type: 'user_updated',
      title: 'Usuario actualizado',
      message: 'Se ha actualizado la información de un usuario',
      userId: users.beatriz.id,
    },
    {
      type: 'case_deleted',
      title: 'Caso eliminado',
      message: 'Se ha eliminado un caso del sistema',
      userId: users.sofia.id,
    },
  ];

  await prisma.notification.deleteMany({});

  for (const notification of notifications) {
    await prisma.notification.create({
      data: notification,
    });
  }
}

export { main };

const isMainModule =
  process.argv[1]?.endsWith('seed.ts') || process.argv[1]?.endsWith('seed.js');

if (isMainModule) {
  main()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect().catch((e) => {
        console.error('Error disconnecting from database:', e);
      });
    });
}
