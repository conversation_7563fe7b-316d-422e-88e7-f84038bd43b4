export const dynamic = 'force-dynamic';

import { PermissionGuard } from '@/components/auth/permission-guard';
import { getAllCases, getCaseStats } from '@/features/case/actions';

import { DashboardHeader } from '../dashboard/components/dashboard-header';
import { CasesContent } from './components/cases-content';

export default async function CasesPage() {
  const [[cases], [stats]] = await Promise.all([getAllCases(), getCaseStats()]);

  return (
    <PermissionGuard
      permission="Ver casos"
      redirectTo="/dashboard"
      fallback={
        <div className="min-h-screen bg-gray-50">
          <DashboardHeader />
          <main className="container mx-auto px-4 py-6">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Acceso Denegado
              </h1>
              <p className="text-gray-600">
                No tienes permisos para ver casos.
              </p>
            </div>
          </main>
        </div>
      }
    >
      <CasesContent
        cases={cases || []}
        stats={
          stats || {
            total: 0,
            byStatus: [],
            byType: [],
            negotiation: 0,
            agreementApproved: 0,
            totalDebt: 0,
          }
        }
      />
    </PermissionGuard>
  );
}
