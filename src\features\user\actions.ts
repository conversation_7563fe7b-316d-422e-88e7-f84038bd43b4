'use server';

import { revalidateTag } from 'next/cache';
import { z } from 'zod';
import bcrypt from 'bcrypt';

import prisma from '@/lib/prisma';
import {
  viewUsersPermissionProcedure,
  createUsersPermissionProcedure,
  editUsersPermissionProcedure,
  deleteUsersPermissionProcedure,
} from './procedures';

import {
  createUserSchema,
  updateUserSchema,
  userSchema,
  userSummarySchema,
} from './schemas';

export const getAllUsers = viewUsersPermissionProcedure
  .createServerAction()
  .output(z.array(userSchema))
  .handler(async () => {
    const users = await prisma.user.findMany({
      include: {
        role: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
        assignedCases: { select: { id: true } },
      },
      orderBy: {
        name: 'asc',
      },
    });

    // Transform the data to match the expected schema
    return users.map((user) => ({
      ...user,
      role: {
        ...user.role,
        permissions: user.role.permissions.map((rp) => rp.permission.name),
      },
    }));
  });

export const createUser = createUsersPermissionProcedure
  .createServerAction()
  .input(createUserSchema)
  .output(userSummarySchema)
  .handler(async ({ input: { roleId, password, ...rest } }) => {
    const hashedPassword = password ? await bcrypt.hash(password, 10) : null;

    const createdUser = await prisma.user.create({
      data: {
        ...rest,
        password: hashedPassword,
        role: { connect: { id: roleId } },
      },
    });

    revalidateTag('users');

    return createdUser;
  });

export const updateUser = editUsersPermissionProcedure
  .createServerAction()
  .input(updateUserSchema)
  .output(userSummarySchema)
  .handler(async ({ input: { id, roleId, password, ...rest } }) => {
    const hashedPassword = password
      ? await bcrypt.hash(password, 10)
      : undefined;

    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        ...rest,
        ...(hashedPassword && { password: hashedPassword }),
        ...(roleId && { role: { connect: { id: roleId } } }),
      },
    });

    revalidateTag('users');

    return updatedUser;
  });

export const deleteUser = deleteUsersPermissionProcedure
  .createServerAction()
  .input(z.string())
  .output(userSummarySchema)
  .handler(async ({ input: id }) => {
    const deletedUser = await prisma.user.delete({ where: { id } });

    revalidateTag('users');

    return deletedUser;
  });
